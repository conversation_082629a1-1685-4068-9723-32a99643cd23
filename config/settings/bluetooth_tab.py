from fabric.bluetooth import BluetoothClient, BluetoothDevice
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.image import Image
from fabric.widgets.label import Label
from fabric.widgets.scrolledwindow import ScrolledWindow
from gi.repository import <PERSON><PERSON><PERSON>, Gtk
import subprocess

import utils.icons as icons


class BluetoothDeviceSlot(CenterBox):
    def __init__(self, device: BluetoothDevice, **kwargs):
        super().__init__(name="bluetooth-device", **kwargs)
        self.device = device
        self.device.connect("changed", self.on_changed)
        self.device.connect(
            "notify::closed", lambda *_: self.device.closed and self.destroy()
        )

        self.connection_label = Label(name="bluetooth-connection", markup=icons.bluetooth_disconnected)
        self.connect_button = Button(
            name="bluetooth-connect",
            label="Connect",
            on_clicked=lambda *_: self._handle_device_action(),
            style_classes=["connected"] if self.device.connected else None,
        )

        # Get device icon
        device_icon = self._get_device_icon(device)

        # Create device name label that we can update
        self.device_name_label = Label(
            label=device.alias or device.name,
            h_expand=True,
            h_align="start",
            ellipsization="end"
        )

        self.start_children = [
            Box(
                spacing=8,
                h_expand=True,
                h_align="fill",
                children=[
                    Label(name="bluetooth-tab-icon", markup=device_icon),
                    self.device_name_label,
                    self.connection_label,
                ],
            )
        ]
        self.end_children = self.connect_button

        self.device.emit("changed")

    def _get_device_icon(self, device: BluetoothDevice) -> str:
        """Get appropriate icon for a bluetooth device based on its type"""
        device_type = device.type.lower()

        if (
            "audio" in device_type
            or "headphone" in device_type
            or "headset" in device_type
        ):
            return icons.headphones
        elif "keyboard" in device_type:
            return icons.keyboard
        elif "phone" in device_type or "mobile" in device_type:
            return icons.bluetooth  # Use bluetooth icon for phones since smartphone doesn't exist
        else:
            return icons.bluetooth

    def _handle_device_action(self):
        """Handle connect/disconnect button click"""
        if self.device.connected:
            # Disconnect
            self.device.connected = False
        else:
            # Connect (this will also handle pairing if needed)
            if not self.device.paired:
                # Show pairing notification before attempting to connect
                self._show_pairing_notification()
            self.device.connected = True

    def _show_pairing_notification(self):
        """Show a pairing notification using notify-send to integrate with notification popup"""
        try:
            device_name = self.device.alias or self.device.name
            # Send notification that will be picked up by the notification popup system
            subprocess.run([
                "notify-send",
                "Bluetooth Pairing Request",
                f"Do you want to pair with {device_name}?",
                "--icon=bluetooth",
                "--urgency=critical",
                "--app-name=Bluetooth",
                "--expire-time=0",  # Don't auto-expire
                f"--hint=string:action-ids:accept,reject",
                f"--hint=string:action-accept:Accept",
                f"--hint=string:action-reject:Reject"
            ], check=False)
            print(f"Sent pairing notification for {device_name}")
        except Exception as e:
            print(f"Failed to send pairing notification: {e}")

    def _show_pairing_success_notification(self):
        """Show notification when pairing succeeds"""
        try:
            device_name = self.device.alias or self.device.name
            subprocess.run([
                "notify-send",
                "Bluetooth Paired",
                f"Successfully paired with {device_name}",
                "--icon=bluetooth",
                "--urgency=normal",
                "--app-name=Bluetooth"
            ], check=False)
        except Exception as e:
            print(f"Failed to send pairing success notification: {e}")

    def _show_pairing_failed_notification(self):
        """Show notification when pairing fails"""
        try:
            device_name = self.device.alias or self.device.name
            subprocess.run([
                "notify-send",
                "Bluetooth Pairing Failed",
                f"Failed to pair with {device_name}",
                "--icon=bluetooth-disabled",
                "--urgency=normal",
                "--app-name=Bluetooth"
            ], check=False)
        except Exception as e:
            print(f"Failed to send pairing failed notification: {e}")

    def on_changed(self, *_):
        # Update connection icon
        self.connection_label.set_markup(
            icons.bluetooth_connected if self.device.connected else icons.bluetooth_disconnected
        )

        # Check for pairing state changes and show notifications
        base_name = self.device.alias or self.device.name

        # Track pairing success/failure
        if hasattr(self, '_was_pairing') and self._was_pairing:
            if self.device.paired and not self.device.connecting:
                # Pairing succeeded
                self._show_pairing_success_notification()
                self._was_pairing = False
            elif not self.device.connecting and not self.device.paired:
                # Pairing failed
                self._show_pairing_failed_notification()
                self._was_pairing = False

        # Update device name with status
        if self.device.connecting:
            if self.device.paired:
                status_text = f"{base_name}\nConnecting..."
                self.connect_button.set_label("Connecting...")
            else:
                status_text = f"{base_name}\nPairing..."
                self.connect_button.set_label("Pairing...")
                self._was_pairing = True  # Track that we're pairing
        elif self.device.connected:
            battery_info = ""
            if self.device.battery_percentage > 0:
                battery_info = f" • {self.device.battery_percentage:.0f}%"
            status_text = f"{base_name}\nConnected{battery_info}"
            self.connect_button.set_label("Disconnect")
        else:
            status_text = base_name
            if self.device.paired:
                self.connect_button.set_label("Connect")
            else:
                self.connect_button.set_label("Pair")

        self.device_name_label.set_label(status_text)

        if self.device.connected:
            self.connect_button.add_style_class("connected")
        else:
            self.connect_button.remove_style_class("connected")


class BluetoothTab:
    """Bluetooth device management tab for settings"""

    def __init__(self):
        self.bluetooth_client = BluetoothClient(on_device_added=self.on_device_added)
        self.device_widgets = {}

        # Connect to bluetooth client signals
        self.bluetooth_client.connect("notify::enabled", self._on_bluetooth_changed)
        self.bluetooth_client.connect("notify::scanning", self._update_scan_button)



    def create_bluetooth_tab(self):
        """Create the Bluetooth tab content"""
        main_vbox = Box(
            orientation="v",
            spacing=0,
            style="padding: 0; margin: 15px;"
        )

        # Set fixed size for the main container to match GUI window
        main_vbox.set_size_request(620, 580)

        # Create widgets first
        self.bluetooth_subtitle = Label(
            markup="<span>Find and connect to Bluetooth devices</span>",
            h_align="start"
        )
        self.bluetooth_switch = Gtk.Switch()
        self.scan_icon = Label(name="bluetooth-tab-icon", markup=icons.radar)
        self.scan_button = Button(
            name="bluetooth-scan",
            child=self.scan_icon,
            tooltip_text="Scan for devices",
            on_clicked=lambda *_: self.bluetooth_client.toggle_scan()
        )

        # Header section with title and controls
        header_box = CenterBox(
            name="bluetooth-header",
            style="margin-bottom: 16px;",
            start_children=[
                Box(
                    orientation="v",
                    spacing=4,
                    children=[
                        Label(
                            markup="<span size='large'><b>Bluetooth</b></span>",
                            h_align="start"
                        ),
                        self.bluetooth_subtitle,
                    ]
                )
            ],
            end_children=[
                Box(
                    orientation="h",
                    spacing=8,
                    children=[
                        self.bluetooth_switch,
                        self.scan_button,
                    ]
                )
            ]
        )

        # Set up switch
        self.bluetooth_switch.set_valign(Gtk.Align.CENTER)
        self.bluetooth_switch.connect("notify::active", self._on_bluetooth_switch_toggled)

        main_vbox.add(header_box)

        # Device containers
        self.paired_box = Box(spacing=2, orientation="v")
        self.available_box = Box(spacing=2, orientation="v")

        content_box = Box(
            spacing=8,
            orientation="v",
            children=[
                Label(name="bluetooth-section", markup="<b>Paired Devices</b>", h_align="start"),
                self.paired_box,
                Label(name="bluetooth-section", markup="<b>Available Devices</b>", h_align="start"),
                self.available_box,
            ]
        )

        # Devices list in scrolled window
        devices_scrolled = ScrolledWindow(
            name="bluetooth-devices",
            h_scrollbar_policy="never",
            v_scrollbar_policy="automatic",
            h_expand=True,
            v_expand=True,
            propagate_width=False,
            propagate_height=False,
            child=content_box,
        )

        devices_scrolled.set_size_request(-1, 500)
        main_vbox.add(devices_scrolled)

        # Initialize bluetooth status and populate existing devices
        self._update_bluetooth_status()
        self._populate_existing_devices()
        self.bluetooth_client.notify("enabled")
        self.bluetooth_client.notify("scanning")

        return main_vbox

    def _update_bluetooth_status(self):
        """Update Bluetooth status display"""
        if self.bluetooth_client.enabled:
            self.bluetooth_switch.set_active(True)
            self.bluetooth_subtitle.set_markup(
                "<span>Find and connect to Bluetooth devices</span>"
            )
            self.scan_button.set_sensitive(True)
        else:
            self.bluetooth_switch.set_active(False)
            self.bluetooth_subtitle.set_markup(
                "<span>Bluetooth is turned off</span>"
            )
            self.scan_button.set_sensitive(False)

    def _on_bluetooth_changed(self, *args):
        """Handle bluetooth state changes"""
        GLib.idle_add(self._update_bluetooth_status)
        GLib.idle_add(self._refresh_all_devices)

    def _update_scan_button(self, *args):
        """Update scan button appearance"""
        if self.bluetooth_client.scanning:
            self.scan_icon.set_markup(icons.loader)
            self.scan_icon.add_style_class("scanning")
            self.scan_button.add_style_class("scanning")
            self.scan_button.set_tooltip_text("Stop scanning for Bluetooth devices")
        else:
            self.scan_icon.set_markup(icons.radar)
            self.scan_icon.remove_style_class("scanning")
            self.scan_button.remove_style_class("scanning")
            self.scan_button.set_tooltip_text("Scan for Bluetooth devices")

    def _populate_existing_devices(self):
        """Populate existing devices when tab is created"""
        if not self.bluetooth_client or not self.bluetooth_client.enabled:
            return

        print(f"Bluetooth enabled: {self.bluetooth_client.enabled}")
        devices = self.bluetooth_client.devices
        print(f"Found {len(devices)} bluetooth devices")

        for device in devices:
            print(f"Device: {device.alias} ({device.address}) - Paired: {device.paired}, Connected: {device.connected}")
            self.on_device_added(self.bluetooth_client, device.address)

    def on_device_added(self, client: BluetoothClient, address: str):
        """Handle device added"""
        if not (device := client.get_device(address)):
            print(f"Could not get device for address: {address}")
            return

        print(f"Adding device slot for: {device.alias} - Paired: {device.paired}, Trusted: {device.trusted}")
        slot = BluetoothDeviceSlot(device)

        if device.paired or device.trusted:
            print(f"Adding {device.alias} to paired devices")
            return self.paired_box.add(slot)
        else:
            print(f"Adding {device.alias} to available devices")
            return self.available_box.add(slot)

    def _on_bluetooth_switch_toggled(self, switch, gparam):
        """Handle Bluetooth switch toggle"""
        is_active = switch.get_active()
        self.bluetooth_client.enabled = is_active

        # Refresh devices when bluetooth is toggled
        if is_active:
            GLib.timeout_add(1000, self._populate_existing_devices)
        else:
            self._clear_all_devices()

    def _clear_all_devices(self):
        """Clear all device widgets"""
        for child in self.paired_box.get_children():
            self.paired_box.remove(child)
        for child in self.available_box.get_children():
            self.available_box.remove(child)
        self.device_widgets.clear()

    def _refresh_all_devices(self):
        """Clear and repopulate all devices"""
        self._clear_all_devices()
        self._populate_existing_devices()




