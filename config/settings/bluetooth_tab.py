import gi
from fabric.bluetooth import BluetoothClient, BluetoothDevice
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.image import Image
from fabric.widgets.label import Label
from fabric.widgets.scrolledwindow import ScrolledWindow
from gi.repository import GLib, Gtk, Gio
from fabric.notifications.service import Notifications

gi.require_version("GnomeBluetooth", "3.0")
from gi.repository import GnomeBluetooth

import utils.icons as icons
from services.bluetooth_agent import BluetoothPairingAgent


class BluetoothDeviceSlot(CenterBox):
    def __init__(self, device: BluetoothDevice, **kwargs):
        super().__init__(name="bluetooth-device", **kwargs)
        self.device = device
        self.device.connect("changed", self.on_changed)
        self.device.connect(
            "notify::closed", lambda *_: self.device.closed and self.destroy()
        )

        self.connection_label = Label(name="bluetooth-connection", markup=icons.bluetooth_disconnected)
        self.connect_button = Button(
            name="bluetooth-connect",
            label="Connect",
            on_clicked=lambda *_: self._handle_device_action(),
            style_classes=["connected"] if self.device.connected else None,
        )

        # Get device icon
        device_icon = self._get_device_icon(device)

        self.start_children = [
            Box(
                spacing=8,
                h_expand=True,
                h_align="fill",
                children=[
                    Label(name="bluetooth-tab-icon", markup=device_icon),
                    Label(label=device.alias or device.name, h_expand=True, h_align="start", ellipsization="end"),
                    self.connection_label,
                ],
            )
        ]
        self.end_children = self.connect_button

        self.device.emit("changed")

    def _get_device_icon(self, device: BluetoothDevice) -> str:
        """Get appropriate icon for a bluetooth device based on its type"""
        device_type = device.type.lower()

        if (
            "audio" in device_type
            or "headphone" in device_type
            or "headset" in device_type
        ):
            return icons.headphones
        elif "keyboard" in device_type:
            return icons.keyboard
        elif "phone" in device_type or "mobile" in device_type:
            return icons.bluetooth  # Use bluetooth icon for phones since smartphone doesn't exist
        else:
            return icons.bluetooth

    def _handle_device_action(self):
        """Handle connect/disconnect/pair button click"""
        if self.device.connected:
            # Disconnect
            self.device.connected = False
        elif self.device.paired or self.device.trusted:
            # Connect to already paired device
            self.device.connected = True
        else:
            # Initiate pairing for unpaired device
            self._initiate_pairing()

    def _initiate_pairing(self):
        """Initiate pairing with an unpaired device"""
        try:
            print(f"Initiating pairing with {self.device.alias}")

            # Try to pair the device using the fabric API
            # This will trigger the pairing agent if needed
            self.device.paired = True

        except Exception as e:
            print(f"Error initiating pairing: {e}")

    def on_changed(self, *_):
        self.connection_label.set_markup(
            icons.bluetooth_connected if self.device.connected else icons.bluetooth_disconnected
        )

        if self.device.connecting:
            self.connect_button.set_label("Connecting...")
        else:
            if self.device.connected:
                self.connect_button.set_label("Disconnect")
            elif self.device.paired or self.device.trusted:
                self.connect_button.set_label("Connect")
            else:
                self.connect_button.set_label("Pair")

        if self.device.connected:
            self.connect_button.add_style_class("connected")
        else:
            self.connect_button.remove_style_class("connected")


class BluetoothTab:
    """Bluetooth device management tab for settings"""

    def __init__(self):
        self.bluetooth_client = BluetoothClient(on_device_added=self.on_device_added)
        self.device_widgets = {}

        # Initialize pairing agent
        self.pairing_agent = BluetoothPairingAgent()

        # Connect to bluetooth client signals
        self.bluetooth_client.connect("notify::enabled", self._on_bluetooth_changed)
        self.bluetooth_client.connect("notify::scanning", self._update_scan_button)

        # Override the client's device filtering to show all devices during scanning
        self._setup_enhanced_device_discovery()



    def create_bluetooth_tab(self):
        """Create the Bluetooth tab content"""
        main_vbox = Box(
            orientation="v",
            spacing=0,
            style="padding: 0; margin: 15px;"
        )

        # Set fixed size for the main container to match GUI window
        main_vbox.set_size_request(620, 580)

        # Create widgets first
        self.bluetooth_subtitle = Label(
            markup="<span>Find and connect to Bluetooth devices</span>",
            h_align="start"
        )
        self.bluetooth_switch = Gtk.Switch()
        self.scan_icon = Label(name="bluetooth-tab-icon", markup=icons.radar)
        self.scan_button = Button(
            name="bluetooth-scan",
            child=self.scan_icon,
            tooltip_text="Scan for devices",
            on_clicked=lambda *_: self.bluetooth_client.toggle_scan()
        )

        # Header section with title and controls
        header_box = CenterBox(
            name="bluetooth-header",
            style="margin-bottom: 16px;",
            start_children=[
                Box(
                    orientation="v",
                    spacing=4,
                    children=[
                        Label(
                            markup="<span size='large'><b>Bluetooth</b></span>",
                            h_align="start"
                        ),
                        self.bluetooth_subtitle,
                    ]
                )
            ],
            end_children=[
                Box(
                    orientation="h",
                    spacing=8,
                    children=[
                        self.bluetooth_switch,
                        self.scan_button,
                    ]
                )
            ]
        )

        # Set up switch
        self.bluetooth_switch.set_valign(Gtk.Align.CENTER)
        self.bluetooth_switch.connect("notify::active", self._on_bluetooth_switch_toggled)

        main_vbox.add(header_box)

        # Device containers
        self.paired_box = Box(spacing=2, orientation="v")
        self.available_box = Box(spacing=2, orientation="v")

        content_box = Box(
            spacing=8,
            orientation="v",
            children=[
                Label(name="bluetooth-section", markup="<b>Paired Devices</b>", h_align="start"),
                self.paired_box,
                Label(name="bluetooth-section", markup="<b>Available Devices</b>", h_align="start"),
                self.available_box,
            ]
        )

        # Devices list in scrolled window
        devices_scrolled = ScrolledWindow(
            name="bluetooth-devices",
            h_scrollbar_policy="never",
            v_scrollbar_policy="automatic",
            h_expand=True,
            v_expand=True,
            propagate_width=False,
            propagate_height=False,
            child=content_box,
        )

        devices_scrolled.set_size_request(-1, 500)
        main_vbox.add(devices_scrolled)

        # Initialize bluetooth status and populate existing devices
        self._update_bluetooth_status()
        self._populate_existing_devices()
        self.bluetooth_client.notify("enabled")
        self.bluetooth_client.notify("scanning")

        return main_vbox

    def _update_bluetooth_status(self):
        """Update Bluetooth status display"""
        if self.bluetooth_client.enabled:
            self.bluetooth_switch.set_active(True)
            self.bluetooth_subtitle.set_markup(
                "<span>Find and connect to Bluetooth devices</span>"
            )
            self.scan_button.set_sensitive(True)
        else:
            self.bluetooth_switch.set_active(False)
            self.bluetooth_subtitle.set_markup(
                "<span>Bluetooth is turned off</span>"
            )
            self.scan_button.set_sensitive(False)

    def _on_bluetooth_changed(self, *args):
        """Handle bluetooth state changes"""
        GLib.idle_add(self._update_bluetooth_status)
        GLib.idle_add(self._refresh_all_devices)

    def _update_scan_button(self, *args):
        """Update scan button appearance"""
        if self.bluetooth_client.scanning:
            self.scan_icon.set_markup(icons.loader)
            self.scan_icon.add_style_class("scanning")
            self.scan_button.add_style_class("scanning")
            self.scan_button.set_tooltip_text("Stop scanning for Bluetooth devices")
        else:
            self.scan_icon.set_markup(icons.radar)
            self.scan_icon.remove_style_class("scanning")
            self.scan_button.remove_style_class("scanning")
            self.scan_button.set_tooltip_text("Scan for Bluetooth devices")

    def _populate_existing_devices(self):
        """Populate existing devices when tab is created"""
        if not self.bluetooth_client or not self.bluetooth_client.enabled:
            return

        print(f"Bluetooth enabled: {self.bluetooth_client.enabled}")
        devices = self.bluetooth_client.devices
        print(f"Found {len(devices)} bluetooth devices")

        for device in devices:
            print(f"Device: {device.alias} ({device.address}) - Paired: {device.paired}, Connected: {device.connected}")
            self.on_device_added(self.bluetooth_client, device.address)

    def _setup_enhanced_device_discovery(self):
        """Set up enhanced device discovery to show all devices when scanning"""
        try:
            # Override the do_get_raw_devices method to show all devices when scanning
            original_method = self.bluetooth_client.do_get_raw_devices

            def enhanced_get_devices():
                all_devs = self.bluetooth_client._client.get_devices()
                if self.bluetooth_client.scanning:
                    # When scanning, show all discovered devices
                    return [dev for dev in all_devs if dev.get_name() is not None]
                else:
                    # When not scanning, show only paired/trusted devices
                    return original_method()

            self.bluetooth_client.do_get_raw_devices = enhanced_get_devices

        except Exception as e:
            print(f"Failed to setup enhanced device discovery: {e}")

    def on_device_added(self, client: BluetoothClient, address: str):
        """Handle device added"""
        if not (device := client.get_device(address)):
            print(f"Could not get device for address: {address}")
            return

        print(f"Adding device slot for: {device.alias} - Paired: {device.paired}, Trusted: {device.trusted}")
        slot = BluetoothDeviceSlot(device)

        if device.paired or device.trusted:
            print(f"Adding {device.alias} to paired devices")
            return self.paired_box.add(slot)
        else:
            print(f"Adding {device.alias} to available devices")
            return self.available_box.add(slot)

    def _on_bluetooth_switch_toggled(self, switch, gparam):
        """Handle Bluetooth switch toggle"""
        is_active = switch.get_active()
        self.bluetooth_client.enabled = is_active

        # Refresh devices when bluetooth is toggled
        if is_active:
            GLib.timeout_add(1000, self._populate_existing_devices)
        else:
            self._clear_all_devices()

    def _clear_all_devices(self):
        """Clear all device widgets"""
        for child in self.paired_box.get_children():
            self.paired_box.remove(child)
        for child in self.available_box.get_children():
            self.available_box.remove(child)
        self.device_widgets.clear()

    def _refresh_all_devices(self):
        """Clear and repopulate all devices"""
        self._clear_all_devices()
        self._populate_existing_devices()




