from fabric.bluetooth import BluetoothClient, BluetoothDevice
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.entry import Entry
from fabric.widgets.label import Label
from fabric.widgets.scrolledwindow import ScrolledWindow
from gi.repository import <PERSON><PERSON><PERSON>, Gtk

import utils.icons as icons


class BluetoothTab:
    """Bluetooth device management tab for settings"""

    def __init__(self):
        self.bluetooth_client = None
        self.device_widgets = {}
        self.connecting_to = None
        self.scanning = False
        
        # Initialize bluetooth client
        self._init_bluetooth_client()

    def _init_bluetooth_client(self):
        """Initialize bluetooth client with error handling"""
        try:
            self.bluetooth_client = BluetoothClient()
            self.bluetooth_client.connect("changed", self._on_bluetooth_changed)
            self.bluetooth_client.connect("device-added", self._on_device_changed)
            self.bluetooth_client.connect("device-removed", self._on_device_changed)
        except Exception as e:
            print(f"Warning: Failed to initialize BluetoothClient: {e}")
            self.bluetooth_client = None

    def _on_bluetooth_changed(self, *args):
        """Handle bluetooth state changes"""
        GLib.idle_add(self._update_bluetooth_status)
        GLib.idle_add(self._refresh_devices)

    def _on_device_changed(self, *args):
        """Handle device list changes"""
        GLib.idle_add(self._refresh_devices)

    def create_bluetooth_tab(self):
        """Create the Bluetooth tab content"""
        main_vbox = Box(
            orientation="v",
            spacing=0,
            style="padding: 0; margin: 15px;"
        )

        # Set fixed size for the main container to match GUI window
        main_vbox.set_size_request(620, 580)  # Match GUI window content area

        # Header section with title and controls
        header_box = Box(
            orientation="h",
            spacing=12,
            style="margin-bottom: 16px;"
        )

        # Bluetooth section
        bluetooth_section = Box(orientation="v", spacing=4)

        # Bluetooth title
        bluetooth_title = Label(
            markup="<span size='large'><b>Bluetooth</b></span>",
            h_align="start"
        )
        bluetooth_section.add(bluetooth_title)

        # Bluetooth subtitle
        self.bluetooth_subtitle = Label(
            markup="<span>Find and connect to Bluetooth devices</span>",
            h_align="start"
        )
        bluetooth_section.add(self.bluetooth_subtitle)

        header_box.add(bluetooth_section)

        # Spacer to push controls to the right
        spacer = Box()
        spacer.set_hexpand(True)
        header_box.add(spacer)

        # Controls section
        controls_box = Box(orientation="h", spacing=8)

        # Bluetooth switch
        self.bluetooth_switch = Gtk.Switch()
        self.bluetooth_switch.set_valign(Gtk.Align.CENTER)
        self.bluetooth_switch.connect("notify::active", self._on_bluetooth_switch_toggled)
        controls_box.add(self.bluetooth_switch)

        # Scan button
        self.scan_button = Button()
        self.scan_icon = Label(name="bluetooth-tab-icon", markup=icons.radar)
        self.scan_button.add(self.scan_icon)
        self.scan_button.set_size_request(40, 40)
        self.scan_button.set_tooltip_text("Scan for devices")
        self.scan_button.connect("clicked", self._on_scan_clicked)
        controls_box.add(self.scan_button)

        header_box.add(controls_box)
        main_vbox.add(header_box)

        # Devices list in scrolled window
        self.devices_scrolled = ScrolledWindow(
            h_scrollbar_policy="never",
            v_scrollbar_policy="automatic",
            h_expand=True,
            v_expand=True,
            propagate_width=False,
            propagate_height=False,
        )

        # Set size to match available space (580 - header space ≈ 500)
        self.devices_scrolled.set_size_request(-1, 500)

        self.devices_container = Box(
            orientation="v",
            spacing=0,
        )
        self.devices_scrolled.add(self.devices_container)
        main_vbox.add(self.devices_scrolled)

        # Initialize bluetooth status and populate devices
        self._update_bluetooth_status()
        self._refresh_devices()

        return main_vbox

    def _update_bluetooth_status(self):
        """Update Bluetooth status display"""
        if not self.bluetooth_client:
            self.bluetooth_subtitle.set_markup(
                "<span>No Bluetooth adapter available</span>"
            )
            self.bluetooth_switch.set_sensitive(False)
            return

        if self.bluetooth_client.enabled:
            self.bluetooth_switch.set_active(True)
            self.bluetooth_subtitle.set_markup(
                "<span>Find and connect to Bluetooth devices</span>"
            )
            self.scan_button.set_sensitive(True)
        else:
            self.bluetooth_switch.set_active(False)
            self.bluetooth_subtitle.set_markup(
                "<span>Bluetooth is turned off</span>"
            )
            self.scan_button.set_sensitive(False)

    def _refresh_devices(self):
        """Refresh the devices list"""
        # Clear existing widgets
        for child in self.devices_container.get_children():
            self.devices_container.remove(child)
        self.device_widgets.clear()

        if not self.bluetooth_client or not self.bluetooth_client.enabled:
            self._show_status_message("Bluetooth is disabled", "info")
            return

        self._populate_devices()

    def _populate_devices(self):
        """Populate the devices list"""
        if not self.bluetooth_client:
            self._show_status_message("No Bluetooth adapter available", "error")
            return

        devices = self.bluetooth_client.devices
        if not devices:
            self._show_status_message(
                "No devices found\nClick 'Scan for Devices' to discover nearby devices",
                "info"
            )
            return

        # Sort devices by connection status and name
        sorted_devices = sorted(
            devices,
            key=lambda device: (not device.connected, device.alias.lower())
        )

        for device in sorted_devices:
            self._create_device_widget(device)

    def _show_status_message(self, message, message_type="info"):
        """Show a status message in the devices container"""
        status_box = Box(
            orientation="v",
            spacing=16,
            style="padding: 60px;"
        )
        status_box.set_halign(Gtk.Align.CENTER)
        status_box.set_valign(Gtk.Align.CENTER)

        # Icon based on message type
        if message_type == "error":
            icon_markup = icons.bluetooth_off
            message_label = Label(
                markup=f"<span size='medium'>{message}</span>",
                h_align="center",
                style="color: var(--error);"
            )
        elif message_type == "scanning":
            icon_markup = icons.loader
            message_label = Label(
                markup=f"<span size='medium'>{message}</span>",
                h_align="center",
                style="color: var(--blue);"
            )
        else:  # info
            icon_markup = icons.bluetooth_off
            message_label = Label(
                markup=f"<span size='medium'>{message}</span>",
                h_align="center",
                style="color: var(--outline);"
            )

        icon_label = Label(name="bluetooth-tab-icon", markup=icon_markup)
        icon_label.set_halign(Gtk.Align.CENTER)
        status_box.add(icon_label)

        message_label.set_line_wrap(True)
        message_label.set_justify(Gtk.Justification.CENTER)
        message_label.set_halign(Gtk.Align.CENTER)
        status_box.add(message_label)

        self.devices_container.add(status_box)
        self.devices_container.show_all()

    def _create_device_widget(self, device: BluetoothDevice):
        """Create a widget for a bluetooth device"""
        # Main container for this device
        device_container = Box(orientation="v", spacing=0)

        # Clickable device button
        device_button = Button(
            style="background-color: transparent; border: none; padding: 0; margin: 0;"
        )
        device_button.connect("clicked", lambda btn, bluetooth_device=device: self._on_device_clicked(bluetooth_device))

        # Device info row
        info_box = Box(orientation="h", spacing=12)
        info_box.set_margin_left(16)
        info_box.set_margin_right(16)
        info_box.set_margin_top(12)
        info_box.set_margin_bottom(12)

        # Device type icon
        device_icon = self._get_device_icon(device)
        icon_label = Label(name="bluetooth-tab-icon", markup=device_icon)
        icon_label.set_size_request(30, -1)
        info_box.add(icon_label)

        # Device name and status
        device_name = device.alias or device.name
        if device.connected:
            battery_info = ""
            if device.battery_percentage > 0:
                battery_info = f" • {device.battery_percentage:.0f}%"
            device_markup = f"<b>{device_name}</b>\nConnected{battery_info}"
            device_label = Label(markup=device_markup, h_align="start", style="color: var(--foreground);")
            device_label.get_style_context().add_class("connected-device")
        elif device.connecting:
            device_markup = f"{device_name}\nConnecting..."
            device_label = Label(markup=device_markup, h_align="start", style="color: var(--blue);")
        else:
            device_markup = f"{device_name}"
            device_label = Label(markup=device_markup, h_align="start", style="color: var(--foreground);")

        device_label.set_line_wrap(True)
        info_box.add(device_label)

        # Store references for later updates
        self.device_widgets[device.address] = {
            'device': device,
            'device_label': device_label,
            'container': device_container
        }

        device_button.add(info_box)
        device_container.add(device_button)
        self.devices_container.add(device_container)

        self.devices_container.show_all()

    def _get_device_icon(self, device: BluetoothDevice) -> str:
        """Get appropriate icon for a bluetooth device based on its type"""
        device_type = device.type.lower()

        if (
            "audio" in device_type
            or "headphone" in device_type
            or "headset" in device_type
        ):
            return icons.headphones
        elif "mouse" in device_type:
            return icons.mouse
        elif "keyboard" in device_type:
            return icons.keyboard
        elif "phone" in device_type:
            return icons.smartphone
        else:
            return icons.bluetooth

    def _on_device_clicked(self, device: BluetoothDevice):
        """Handle device item click"""
        if device.connected:
            # If connected, disconnect from this device
            self._disconnect_device(device)
        else:
            # If not connected, connect to this device
            self._connect_device(device)

    def _on_bluetooth_switch_toggled(self, switch, gparam):
        """Handle Bluetooth switch toggle"""
        if self.bluetooth_client:
            is_active = switch.get_active()
            self.bluetooth_client.enabled = is_active
            GLib.timeout_add(500, self._refresh_devices)

    def _on_scan_clicked(self, button):
        """Handle scan button click"""
        if self.bluetooth_client and self.bluetooth_client.enabled:
            self.scan_icon.set_markup(icons.loader)
            button.set_sensitive(False)
            self.scanning = True

            # Clear current devices and show scanning message
            for child in self.devices_container.get_children():
                self.devices_container.remove(child)
            self._show_status_message("Scanning for devices...", "scanning")

            self.bluetooth_client.scan()

            # Re-enable button after scan
            GLib.timeout_add(10000, lambda: [
                self.scan_icon.set_markup(icons.radar),
                button.set_sensitive(True),
                setattr(self, 'scanning', False),
                self._refresh_devices()
            ])

    def _connect_device(self, device: BluetoothDevice):
        """Connect to a bluetooth device"""
        if not device or device.connected:
            return

        self.connecting_to = device.address

        # Update device widget to show connecting state
        if device.address in self.device_widgets:
            device_label = self.device_widgets[device.address]['device_label']
            device_label.set_markup(f"{device.alias}\nConnecting...")
            device_label.set_style("color: var(--blue);")

        try:
            # Connect to device
            device.connected = True
            # Reset state after timeout if still connecting
            GLib.timeout_add(15000, lambda: self._reset_connection_state(device.address))
        except Exception as e:
            self._show_connection_error(device.address, f"Connection error: {str(e)}")
            self._reset_connection_state(device.address)

    def _disconnect_device(self, device: BluetoothDevice):
        """Disconnect from a bluetooth device"""
        if not device or not device.connected:
            return

        try:
            device.connected = False
            self._refresh_devices()
        except Exception as e:
            print(f"Error disconnecting from {device.alias}: {e}")

    def _show_connection_error(self, device_address, error_message):
        """Show connection error message"""
        print(f"Bluetooth connection error for {device_address}: {error_message}")

    def _reset_connection_state(self, device_address):
        """Reset connection state for a device"""
        if device_address in self.device_widgets:
            # Reset the device label to normal state
            device = self.device_widgets[device_address]['device']
            device_label = self.device_widgets[device_address]['device_label']
            if device.connected:
                battery_info = ""
                if device.battery_percentage > 0:
                    battery_info = f" • {device.battery_percentage:.0f}%"
                device_markup = f"<b>{device.alias}</b>\nConnected{battery_info}"
                device_label.set_style("color: var(--foreground);")
                device_label.get_style_context().add_class("connected-device")
            else:
                device_markup = f"{device.alias}"
                device_label.set_style("color: var(--foreground);")
            device_label.set_markup(device_markup)

        self.connecting_to = None
        self._refresh_devices()
        return False
