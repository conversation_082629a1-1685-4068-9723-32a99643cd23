from config.data import (
    APP_NAME,
    APP_NAME_CAP,
)

SOURCE_STRING = f"""
# {APP_NAME_CAP}
source = ~/.config/{APP_NAME_CAP}/config/hypr/{APP_NAME}.conf
"""

DEFAULTS = {
    "prefix_restart": "ALT SHIFT",
    "suffix_restart": "R",
    "prefix_msg": "SUPER",
    "suffix_msg": "A",
    "prefix_application_switcher": "ALT",
    "suffix_application_switcher": "TAB",
    "prefix_kanban": "SUPER",
    "suffix_kanban": "T",
    "prefix_launcher": "SUPER",
    "suffix_launcher": "SPACE",
    "suffix_app_launcher": "D",
    "prefix_app_launcher": "SUPER",
    "prefix_cliphist": "SUPER",
    "suffix_cliphist": "V",
    "prefix_wallpapers": "SUPER",
    "suffix_wallpapers": "W",
    "prefix_randwall": "ALT SHIFT",
    "suffix_randwall": "W",
    "prefix_emoji": "SUPER",
    "suffix_emoji": "E",
    "prefix_power": "SUPER",
    "suffix_power": "ESCAPE",
    "prefix_caffeine": "SUPER SHIFT",
    "suffix_caffeine": "M",
    "prefix_restart_inspector": "SUPER CTRL ALT",
    "suffix_restart_inspector": "B",
    "prefix_settings": "SUPER",
    "suffix_settings": "I",
    "wallpapers_dir": "/home/<USER>/Pictures/wallpapers",
    "vertical": True,
    "dock_enabled": True,
    "dock_always_occluded": False,
    "dock_auto_hide": True,
    "terminal_command": "kitty -e",
    "corners_visible": False,
    "dock_theme": "Pills",
    "notif_pos": "Top",
    "dock_position": "Bottom",
    "dock_icon_size": 20,
    "dock_workspaces_visible": True,
    "dock_metrics_visible": True,
    "dock_battery_visible": True,
    "dock_date_time_visible": True,
    "dock_controls_visible": True,
    "dock_indicators_visible": True,
    "dock_music_player_visible": True,
    "dock_notifications_visible": True,
    "dock_applications_visible": True,
    "dock_language_visible": True,
    "dock_hide_special_workspace": True,
    "dock_hide_special_workspace_apps": True,
    "workspace_nums": False,
    "workspace_use_chinese_numerals": False,
    "workspace_dots": False,
    "window_switcher_items_per_row": 13,
    "metrics_visible": {
        "cpu": True,
        "ram": True,
        "disk": True,
        "swap": True,
        "gpu": False,
    },
    "metrics_disks": ["/"],
    "limited_apps_history": ["Spotify"],
    "history_ignored_apps": ["Modus"],
}
