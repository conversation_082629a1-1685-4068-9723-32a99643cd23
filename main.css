@import url("./styles/dock.css");
@import url("./styles/colors.css");
@import url("./styles/components.css");
@import url("./styles/controls.css");
@import url("./styles/indicators.css");
@import url("./styles/workspaces.css");
@import url("./styles/switcher.css");
@import url("./styles/osd.css");
@import url("./styles/launcher.css");
@import url("./styles/kanban.css");
@import url("./styles/calendar.css");
@import url("./styles/notification.css");
@import url("./styles/player.css");

* {
  all: unset;
  color: var(--foreground);
  font-size: unset;
  font-family: unset;
  border-radius: 16px;
}

#corner {
  background-color: var(--shadow);
  border-radius: 0;
}

#corner-container {
  min-width: 20px;
  min-height: 20px;
}

tooltip {
  border: solid 1px;
  border-color: var(--surface);
  background-color: var(--shadow);
  animation: tooltipShow 0.25s cubic-bezier(0.5, 0.25, 0, 1);
}

@keyframes tooltipShow {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

tooltip > * {
  padding: 6px 10px;
  border-radius: 10px;
}




#workspaces,
#workspaces-container,
#launcher,
#date-time,
#music-player,
#osd,
#tray,
#battery,
#controls,
#notification-stack-box,
#metrics,
#language,
#applications-dock,
#dock {
  box-shadow: 0 0 3px alpha(black, 0.7);
}

