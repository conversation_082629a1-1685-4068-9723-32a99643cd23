import gi
from gi.repository import <PERSON><PERSON><PERSON>, <PERSON><PERSON>
from fabric.notifications.service import Notifications
import subprocess
import threading

gi.require_version("GnomeBluetooth", "3.0")


class BluetoothPairingAgent:
    """
    Bluetooth pairing agent that handles pairing requests and shows notifications
    """
    
    AGENT_PATH = "/org/bluez/agent/modus"
    AGENT_INTERFACE = "org.bluez.Agent1"
    BLUEZ_SERVICE = "org.bluez"
    AGENT_MANAGER_PATH = "/org/bluez"
    AGENT_MANAGER_INTERFACE = "org.bluez.AgentManager1"
    
    def __init__(self):
        self.bus = None
        self.agent_id = None
        self.pending_requests = {}
        self.notifications = None
        self._setup_dbus_agent()
    
    def _setup_dbus_agent(self):
        """Set up D-Bus agent for Bluetooth pairing"""
        try:
            # Get the system bus
            self.bus = Gio.bus_get_sync(Gio.BusType.SYSTEM, None)
            
            # Initialize notifications
            try:
                self.notifications = Notifications()
            except Exception:
                print("Could not initialize notifications service")
            
            # Register the agent
            self._register_agent()
            
        except Exception as e:
            print(f"Failed to setup Bluetooth pairing agent: {e}")
    
    def _register_agent(self):
        """Register the pairing agent with <PERSON><PERSON>"""
        try:
            # Create the agent object
            introspection_xml = """
            <node>
                <interface name='org.bluez.Agent1'>
                    <method name='RequestPinCode'>
                        <arg type='o' name='device' direction='in'/>
                        <arg type='s' name='pincode' direction='out'/>
                    </method>
                    <method name='DisplayPinCode'>
                        <arg type='o' name='device' direction='in'/>
                        <arg type='s' name='pincode' direction='in'/>
                    </method>
                    <method name='RequestPasskey'>
                        <arg type='o' name='device' direction='in'/>
                        <arg type='u' name='passkey' direction='out'/>
                    </method>
                    <method name='DisplayPasskey'>
                        <arg type='o' name='device' direction='in'/>
                        <arg type='u' name='passkey' direction='in'/>
                        <arg type='q' name='entered' direction='in'/>
                    </method>
                    <method name='RequestConfirmation'>
                        <arg type='o' name='device' direction='in'/>
                        <arg type='u' name='passkey' direction='in'/>
                    </method>
                    <method name='RequestAuthorization'>
                        <arg type='o' name='device' direction='in'/>
                    </method>
                    <method name='AuthorizeService'>
                        <arg type='o' name='device' direction='in'/>
                        <arg type='s' name='uuid' direction='in'/>
                    </method>
                    <method name='Cancel'/>
                    <method name='Release'/>
                </interface>
            </node>
            """
            
            # Register the object
            self.agent_id = self.bus.register_object(
                self.AGENT_PATH,
                Gio.DBusNodeInfo.new_for_xml(introspection_xml).interfaces[0],
                self._handle_method_call,
                None,
                None
            )
            
            # Register with AgentManager
            self._call_agent_manager("RegisterAgent", 
                                   GLib.Variant("(os)", (self.AGENT_PATH, "KeyboardDisplay")))
            
            # Request to be the default agent
            self._call_agent_manager("RequestDefaultAgent", 
                                   GLib.Variant("(o)", (self.AGENT_PATH,)))
            
            print("Bluetooth pairing agent registered successfully")
            
        except Exception as e:
            print(f"Failed to register Bluetooth agent: {e}")
    
    def _call_agent_manager(self, method_name, parameters=None):
        """Call a method on the AgentManager"""
        try:
            self.bus.call_sync(
                self.BLUEZ_SERVICE,
                self.AGENT_MANAGER_PATH,
                self.AGENT_MANAGER_INTERFACE,
                method_name,
                parameters,
                None,
                Gio.DBusCallFlags.NONE,
                -1,
                None
            )
        except Exception as e:
            print(f"Failed to call {method_name}: {e}")
    
    def _handle_method_call(self, connection, sender, object_path, interface_name, 
                           method_name, parameters, invocation):
        """Handle D-Bus method calls"""
        try:
            if method_name == "RequestConfirmation":
                device_path, passkey = parameters.unpack()
                self._handle_request_confirmation(device_path, passkey, invocation)
            elif method_name == "RequestAuthorization":
                device_path = parameters.unpack()[0]
                self._handle_request_authorization(device_path, invocation)
            elif method_name == "DisplayPasskey":
                device_path, passkey, entered = parameters.unpack()
                self._handle_display_passkey(device_path, passkey, invocation)
            elif method_name == "RequestPinCode":
                device_path = parameters.unpack()[0]
                self._handle_request_pin_code(device_path, invocation)
            elif method_name == "Cancel":
                self._handle_cancel(invocation)
            elif method_name == "Release":
                self._handle_release(invocation)
            else:
                invocation.return_error_literal(
                    Gio.dbus_error_quark(),
                    Gio.DBusError.UNKNOWN_METHOD,
                    f"Unknown method: {method_name}"
                )
        except Exception as e:
            print(f"Error handling method call {method_name}: {e}")
            invocation.return_error_literal(
                Gio.dbus_error_quark(),
                Gio.DBusError.FAILED,
                str(e)
            )
    
    def _get_device_info(self, device_path):
        """Get device information from D-Bus path"""
        try:
            result = self.bus.call_sync(
                self.BLUEZ_SERVICE,
                device_path,
                "org.freedesktop.DBus.Properties",
                "GetAll",
                GLib.Variant("(s)", ("org.bluez.Device1",)),
                None,
                Gio.DBusCallFlags.NONE,
                -1,
                None
            )
            properties = result.unpack()[0]
            return {
                'name': properties.get('Name', GLib.Variant('s', 'Unknown')).get_string(),
                'address': properties.get('Address', GLib.Variant('s', 'Unknown')).get_string(),
                'alias': properties.get('Alias', GLib.Variant('s', 'Unknown')).get_string()
            }
        except Exception as e:
            print(f"Failed to get device info: {e}")
            return {'name': 'Unknown Device', 'address': 'Unknown', 'alias': 'Unknown Device'}
    
    def _handle_request_confirmation(self, device_path, passkey, invocation):
        """Handle pairing confirmation request"""
        device_info = self._get_device_info(device_path)
        device_name = device_info['alias'] or device_info['name']
        
        print(f"Pairing confirmation requested for {device_name} with passkey {passkey}")
        
        # Store the invocation for later response
        request_id = f"confirm_{device_path}_{passkey}"
        self.pending_requests[request_id] = invocation
        
        # Show notification
        self._show_pairing_notification(
            device_name, 
            device_info['address'],
            f"Confirm pairing with {device_name}?\nPasskey: {passkey:06d}",
            request_id
        )
    
    def _handle_request_authorization(self, device_path, invocation):
        """Handle authorization request"""
        device_info = self._get_device_info(device_path)
        device_name = device_info['alias'] or device_info['name']
        
        print(f"Authorization requested for {device_name}")
        
        # Store the invocation for later response
        request_id = f"auth_{device_path}"
        self.pending_requests[request_id] = invocation
        
        # Show notification
        self._show_pairing_notification(
            device_name,
            device_info['address'], 
            f"Allow {device_name} to connect?",
            request_id
        )
    
    def _handle_display_passkey(self, device_path, passkey, invocation):
        """Handle display passkey request"""
        device_info = self._get_device_info(device_path)
        device_name = device_info['alias'] or device_info['name']
        
        print(f"Display passkey {passkey:06d} for {device_name}")
        
        # Show notification with passkey
        self._show_info_notification(
            f"Bluetooth Pairing",
            f"Enter this passkey on {device_name}:\n{passkey:06d}"
        )
        
        # Return success immediately
        invocation.return_value(None)
    
    def _handle_request_pin_code(self, device_path, invocation):
        """Handle PIN code request"""
        device_info = self._get_device_info(device_path)
        device_name = device_info['alias'] or device_info['name']
        
        print(f"PIN code requested for {device_name}")
        
        # For simplicity, use a default PIN (in real implementation, show input dialog)
        pin_code = "0000"
        invocation.return_value(GLib.Variant("(s)", (pin_code,)))
    
    def _handle_cancel(self, invocation):
        """Handle cancel request"""
        print("Pairing cancelled")
        # Clear pending requests
        self.pending_requests.clear()
        invocation.return_value(None)
    
    def _handle_release(self, invocation):
        """Handle release request"""
        print("Agent released")
        invocation.return_value(None)
    
    def _show_pairing_notification(self, device_name, device_address, message, request_id):
        """Show pairing notification with Accept/Reject actions"""
        try:
            # Use notify-send with actions
            subprocess.Popen([
                "notify-send",
                "Bluetooth Pairing Request",
                message,
                "-i", "bluetooth",
                "-u", "critical",
                "-t", "0",  # Don't auto-dismiss
                "-A", f"accept_{request_id},Accept",
                "-A", f"reject_{request_id},Reject"
            ])
            
            print(f"Pairing notification sent for {device_name}")
            
        except Exception as e:
            print(f"Failed to send pairing notification: {e}")
            # Auto-reject if notification fails
            self.handle_pairing_response(request_id, "reject")
    
    def _show_info_notification(self, title, message):
        """Show informational notification"""
        try:
            subprocess.Popen([
                "notify-send",
                title,
                message,
                "-i", "bluetooth",
                "-u", "normal",
                "-t", "10000"  # Auto-dismiss after 10 seconds
            ])
        except Exception as e:
            print(f"Failed to send info notification: {e}")
    
    def handle_pairing_response(self, request_id, action):
        """Handle user response to pairing request"""
        if request_id not in self.pending_requests:
            print(f"No pending request for {request_id}")
            return
            
        invocation = self.pending_requests.pop(request_id)
        
        if action == "accept":
            print(f"User accepted pairing request {request_id}")
            invocation.return_value(None)
        else:
            print(f"User rejected pairing request {request_id}")
            invocation.return_error_literal(
                Gio.dbus_error_quark(),
                Gio.DBusError.AUTH_FAILED,
                "Pairing rejected by user"
            )
    
    def cleanup(self):
        """Clean up the agent"""
        try:
            if self.agent_id and self.bus:
                self.bus.unregister_object(self.agent_id)
            
            if self.bus:
                self._call_agent_manager("UnregisterAgent", 
                                       GLib.Variant("(o)", (self.AGENT_PATH,)))
            
            print("Bluetooth pairing agent cleaned up")
            
        except Exception as e:
            print(f"Failed to cleanup agent: {e}")
