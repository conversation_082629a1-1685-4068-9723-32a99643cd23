#music-player {
  background-color: var(--surface);
  padding: 4px;
}

#music-player.invert {
  border-radius: 12px;
}

#player-circle {
  padding: 4px;
  color: var(--surface-bright);
  border: 3px solid var(--primary);
}
#music-label {
  font-size: 16px;
}

/*
 * ── language indicator ────────────────────────────────────────────────
*/

#language {
  background-color: var(--surface);
  padding: 8px;
}

#language.invert {
  border-radius: 12px;
}

#lang-label {
  font-weight: bold;
}

#lang-label.icon {
  color: var(--primary);
  font-size: 20px;
}

/*
 * ── battery ───────────────────────────────────────────────────────────
*/

#battery {
  background-color: var(--surface);
  padding: 4px;
}

#battery.invert {
  border-radius: 12px;
}

#battery-circle {
  color: var(--surface-bright);
  border: 3px solid var(--primary);
}

#battery-circle.alert {
  border: 2px solid var(--red-dim);
}

#battery-icon {
  font-size: 16px;
}

#battery-icon.alert {
  color: var(--red-dim);
}

#battery-level {
  color: var(--primary);
  font-weight: bold;
  margin: 0 4px;
}

#battery-circle.power-saver {
  border: 3px solid var(--green);
}

#battery-circle.performance {
  border: 3px solid var(--red);
}

#battery-circle.discharging {
  border: 3px solid var(--yellow);
}

#battery-circle.discharging-low {
  border: 3px solid var(--orange);
}

#battery-circle.discharging-critical {
  border: 3px solid var(--red-dim);
}

#battery-icon.discharging {
  color: var(--yellow);
}

#battery-icon.discharging-low {
  color: var(--orange);
}

#battery-icon.discharging-critical {
  color: var(--red-dim);
}

/*
 * ── battery popup ─────────────────────────────────────────────────────
*/

#battery-popup-content {
  background-color: var(--shadow);
  border: 1px solid var(--surface);
  border-radius: 12px;
  padding: 16px;
  margin: 8px;
}

.power-profiles {
  padding-top: 12px;
  margin-top: 8px;
}

#bat-icon {
  font-size: 18px;
}

.power-profile-btn {
  background-color: var(--surface);
  border-radius: 8px;
  padding: 10px 20px;
  transition: all 0.2s ease;
}
.power-profile-btn:hover,
.power-profile-btn:focus {
  background-color: var(--surface-bright);
}

.power-profile-btn.active {
  background-color: var(--primary);
}

.power-profile-btn.active #bat-icon {
  color: var(--shadow);
}

/* Battery info styles */
.battery-percentage {
  font-size: 24px;
  font-weight: bold;
  color: var(--primary);
}

.battery-state {
  font-size: 14px;
  color: var(--text-dim);
}

.battery-label {
  font-size: 12px;
  color: var(--text-dim);
  min-width: 80px;
}

.battery-value {
  font-size: 12px;
  color: var(--text);
  font-weight: 500;
}

#percentage-state-box {
  margin-bottom: 8px;
}

#capacity-box,
#time-box,
#profile-label-box {
  margin-bottom: 4px;
}

/*
 * ── metrics ───────────────────────────────────────────────────────────
*/

#metrics {
  background-color: var(--surface);
  padding: 4px;
}

#metrics.invert {
  background-color: var(--surface);
  border-radius: 12px;
}

#metrics-circle {
  padding: 4px;
  color: var(--surface-bright);
  border: 3px solid var(--primary);
}

#metrics-icon {
  font-size: 16px;
}

#metrics-icon.alert {
  color: var(--red-dim);
}

#metrics-level {
  color: var(--primary);
  font-weight: bold;
  margin: 0 4px;
}

#metrics-sep {
  min-width: 4px;
}

/*
 * ── datetime ──────────────────────────────────────────────────────────
*/

#date-time {
  background-color: var(--surface);
  padding: 8px;
  font-weight: bold;
}
#date-time.vertical {
  padding: 8px 0;
}
#date-time.invert {
  background-color: var(--surface);
  border-radius: 12px;
}

/*
 * ── tray ──────────────────────────────────────────────────────────────
*/

#tray {
  background-color: var(--surface);
  padding: 8px;
}

#tray.invert {
  border-radius: 12px;
}

menu {
  border: solid 1px;
  border-radius: 16px;
  border-color: var(--surface);
  background-color: var(--shadow);
  padding: 6px;
}

menu > menuitem {
  border-radius: 10px;
  padding: 6px 10px;
}

menu > menuitem:hover {
  background-color: var(--primary);
}

menu > menuitem:hover > label {
  color: var(--shadow);
}
