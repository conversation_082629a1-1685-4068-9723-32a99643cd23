#indicator {
  padding: 4px;
  background-color: var(--surface);
}

#indicator.invert {
  background-color: var(--surface);
  border-radius: 12px;
}

#button-network,
#button-bluetooth {
  color: var(--surface-bright);
  border: 3px solid var(--primary);
}

#button-bar-recording {
  border-radius: 12px;
  padding: 4px;
  animation: recording-blink 1.5s ease-in-out infinite;
}

#button-bar-recording:hover {
  background-color: var(--red);
}

@keyframes recording-blink {
  0% {
    background-color: var(--red-dim);
  }
  50% {
    background-color: var(--red-dim);
  }
  100% {
    background-color: var(--red-dim);
  }
}

#network-label,
#bluetooth-label,
#recording-label,
#notification-icon {
  font-size: 20px;
}

#button-bar-notifications {
  border-radius: 12px;
  padding: 4px;
}

#button-bar-notifications:hover {
  background-color: var(--surface-bright);
}
